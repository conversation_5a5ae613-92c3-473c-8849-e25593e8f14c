pub mod constants;
pub mod error;
pub mod instructions;
pub mod state;
pub mod adapters;
pub mod routing;
pub mod flash_loan;
pub mod arbitrage;
pub mod utils;

use anchor_lang::prelude::*;

pub use constants::*;
pub use error::*;
// Events are now in state::event
pub use instructions::*;
pub use state::*;
pub use adapters::*;
pub use routing::*;
pub use flash_loan::*;
pub use arbitrage::*;
pub use utils::*;

declare_id!("jLZjkcwFvyJSv8SwAq6oZH2NiWna6cyxs86hcM9dBiB");

#[program]
pub mod dex_router {
    use super::*;

    /// 初始化路由器配置
    /// 设置全局配置参数，如支持的DEX列表、费率等
    pub fn initialize_config(
        ctx: Context<InitializeConfig>,
        config_data: ConfigArgs,
    ) -> Result<()> {
        initialize_config_handler(ctx, config_data)
    }

    /// 初始化用户位置
    /// 为新用户创建位置跟踪账户
    pub fn initialize_user_position(
        ctx: Context<InitializeUserPosition>,
    ) -> Result<()> {
        initialize_user_position_handler(ctx)
    }


    /// 执行路由交换
    /// 根据路由配置执行单步或多步交换
    pub fn swap<'info>(
        ctx: Context<'_, '_, 'info, 'info, SwapAccounts<'info>>,
        args: SwapArgs,
        order_id: u64,
    ) -> Result<()> {
        swap_handler(ctx, args, order_id)
    }
}
