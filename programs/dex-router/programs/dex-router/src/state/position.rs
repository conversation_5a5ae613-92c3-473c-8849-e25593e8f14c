//! 用户位置和状态管理
//!
//! 跟踪用户的交易历史、风险评级和状态

use anchor_lang::prelude::*;

/// 用户位置状态
/// 记录用户的交易统计和风险管理信息
#[account]
#[derive(Debug)]
pub struct UserPosition {
    /// 用户公钥
    pub owner: Pubkey,

    /// 总交易量（以USDC计价）
    pub total_volume: u64,

    /// 今日交易量
    pub daily_volume: u64,

    /// 今日交易量重置时间戳
    pub daily_volume_reset_at: i64,

    /// 成功交易次数
    pub successful_trades: u32,

    /// 失败交易次数
    pub failed_trades: u32,

    /// 总利润（以USDC计价）
    pub total_profit: i64,

    /// 最大单次损失
    pub max_loss: u64,

    /// 当前风险评分（0-10）
    pub risk_score: u8,

    /// 用户等级（0-5）
    pub user_level: u8,

    /// 是否被暂停
    pub is_suspended: bool,

    /// 暂停原因
    pub suspension_reason: u8,

    /// 暂停时间戳
    pub suspended_at: i64,

    /// 最后活动时间戳
    pub last_activity_at: i64,

    /// 首次交易时间戳
    pub first_trade_at: i64,

    /// 偏好的DEX列表（位标志）
    pub preferred_dex_flags: u64,

    /// 最大允许滑点偏好（基点）
    pub preferred_max_slippage_bps: u16,

    /// 风险偏好等级（0-5，0最保守）
    pub risk_preference: u8,

    /// 保留字段用于未来扩展
    pub reserved: [u64; 6],

    // 新增字段，用于指令约束和统计
    /// 交易次数（总数）
    pub trade_count: u64,

    /// 最后交易时间戳
    pub last_trade_timestamp: i64,

    /// 盈利交易次数
    pub profitable_trades: u32,

    /// 闪电贷使用次数
    pub flash_loan_count: u32,

    /// 套利次数
    pub arbitrage_count: u32,

    /// 成功套利次数
    pub successful_arbitrages: u32,

    /// 套利总利润
    pub total_arbitrage_profit: u64,

    /// 最后套利时间戳
    pub last_arbitrage_timestamp: i64,

    /// 是否获批使用闪电贷
    pub flash_loan_approved: bool,
}

impl UserPosition {
    /// 账户空间大小
    pub const LEN: usize = 8 + // discriminator
        32 + // owner
        8 +  // total_volume
        8 +  // daily_volume
        8 +  // daily_volume_reset_at
        4 +  // successful_trades
        4 +  // failed_trades
        8 +  // total_profit
        8 +  // max_loss
        1 +  // risk_score
        1 +  // user_level
        1 +  // is_suspended
        1 +  // suspension_reason
        8 +  // suspended_at
        8 +  // last_activity_at
        8 +  // first_trade_at
        8 +  // preferred_dex_flags
        2 +  // preferred_max_slippage_bps
        1 +  // risk_preference
        48;  // reserved

    /// 创建新用户位置
    pub fn new(owner: Pubkey, current_timestamp: i64) -> Self {
        Self {
            owner,
            total_volume: 0,
            daily_volume: 0,
            daily_volume_reset_at: current_timestamp,
            successful_trades: 0,
            failed_trades: 0,
            total_profit: 0,
            max_loss: 0,
            risk_score: 5, // 中等风险
            user_level: 0, // 新手
            is_suspended: false,
            suspension_reason: 0,
            suspended_at: 0,
            last_activity_at: current_timestamp,
            first_trade_at: current_timestamp,
            preferred_dex_flags: 0xFFFFFFFFFFFFFFFF, // 默认所有DEX
            preferred_max_slippage_bps: 300, // 3%
            risk_preference: 2, // 中等风险偏好
            reserved: [0; 6],
            // 新增字段的默认值
            trade_count: 0,
            last_trade_timestamp: current_timestamp,
            profitable_trades: 0,
            flash_loan_count: 0,
            arbitrage_count: 0,
            successful_arbitrages: 0,
            total_arbitrage_profit: 0,
            last_arbitrage_timestamp: 0,
            flash_loan_approved: false, // 默认未批准闪电贷
        }
    }

    /// 更新日交易量
    pub fn update_daily_volume(&mut self, amount: u64, current_timestamp: i64) -> Result<()> {
        // 检查是否需要重置日交易量
        let day_in_seconds = 24 * 60 * 60;
        if current_timestamp - self.daily_volume_reset_at >= day_in_seconds {
            self.daily_volume = 0;
            self.daily_volume_reset_at = current_timestamp;
        }

        self.daily_volume = self.daily_volume.checked_add(amount)
            .ok_or(crate::error::RouteError::MathOverflow)?;

        self.total_volume = self.total_volume.checked_add(amount)
            .ok_or(crate::error::RouteError::MathOverflow)?;

        self.last_activity_at = current_timestamp;
        Ok(())
    }

    /// 记录成功交易
    pub fn record_successful_trade(&mut self, profit: i64) -> Result<()> {
        self.successful_trades = self.successful_trades.checked_add(1)
            .ok_or(crate::error::RouteError::MathOverflow)?;

        self.total_profit = self.total_profit.checked_add(profit)
            .ok_or(crate::error::RouteError::MathOverflow)?;

        // 更新用户等级
        self.update_user_level();

        // 更新风险评分（成功交易降低风险）
        if self.risk_score > 1 {
            self.risk_score = self.risk_score.saturating_sub(1);
        }

        Ok(())
    }

    /// 记录失败交易
    pub fn record_failed_trade(&mut self, loss: u64) -> Result<()> {
        self.failed_trades = self.failed_trades.checked_add(1)
            .ok_or(crate::error::RouteError::MathOverflow)?;

        if loss > self.max_loss {
            self.max_loss = loss;
        }

        // 更新风险评分（失败交易增加风险）
        if self.risk_score < 10 {
            self.risk_score = self.risk_score.saturating_add(1);
        }

        Ok(())
    }

    /// 更新用户等级
    fn update_user_level(&mut self) {
        let total_trades = self.successful_trades + self.failed_trades;

        self.user_level = match total_trades {
            0..=10 => 0,      // 新手
            11..=50 => 1,     // 初级
            51..=200 => 2,    // 中级
            201..=1000 => 3,  // 高级
            1001..=5000 => 4, // 专家
            _ => 5,           // 大师
        };
    }

    /// 计算成功率
    pub fn success_rate(&self) -> f64 {
        let total = self.successful_trades + self.failed_trades;
        if total == 0 {
            0.0
        } else {
            (self.successful_trades as f64) / (total as f64)
        }
    }

    /// 检查是否可以执行交易
    pub fn can_trade(&self, config: &super::config::RouterConfig) -> Result<()> {
        // 检查暂停状态
        require!(!self.is_suspended, crate::error::RouteError::UserSuspended);

        // 检查日交易量限制
        require!(
            self.daily_volume <= config.max_daily_volume_per_user,
            crate::error::RouteError::RateLimitExceeded
        );

        Ok(())
    }

    /// 暂停用户
    pub fn suspend(&mut self, reason: u8, timestamp: i64) {
        self.is_suspended = true;
        self.suspension_reason = reason;
        self.suspended_at = timestamp;
    }

    /// 恢复用户
    pub fn resume(&mut self) {
        self.is_suspended = false;
        self.suspension_reason = 0;
        self.suspended_at = 0;
    }

    /// 检查用户风险等级
    pub fn get_risk_level(&self) -> RiskLevel {
        match self.risk_score {
            0..=2 => RiskLevel::Low,
            3..=5 => RiskLevel::Medium,
            6..=7 => RiskLevel::High,
            8..=10 => RiskLevel::Critical,
            _ => RiskLevel::Critical,
        }
    }

    /// 评估路由风险
    pub fn evaluate_route_risk(&self, config: &crate::constants::RouteConfig) -> Result<RouteRiskEvaluation> {
        let mut risk_score = 0u8;
        let mut warnings = Vec::new();

        // 基于用户历史风险评分
        risk_score += self.risk_score / 2; // 用户风险贡献

        // 基于路由复杂度
        let route_steps = config.routes.len();
        if route_steps > 3 {
            risk_score += 2;
            warnings.push("路由步骤较多，增加失败风险".to_string());
        }

        // 基于金额大小
        if config.amount_in > 50_000_000_000 { // 50,000 USDC
            risk_score += 2;
            warnings.push("交易金额较大，增加风险".to_string());
        }

        // 基于滑点设置
        if config.max_slippage_bps > 500 { // 5%
            risk_score += 3;
            warnings.push("滑点设置过高，可能导致损失".to_string());
        }

        // 基于闪电贷使用
        if config.flash_loan.is_some() {
            risk_score += 1;
            warnings.push("使用闪电贷增加复杂度".to_string());
        }

        let is_approved = risk_score <= 8; // 风险评分8以下批准

        Ok(RouteRiskEvaluation {
            risk_score,
            warnings,
            is_approved,
            recommended_max_amount: if risk_score <= 5 {
                config.amount_in
            } else {
                config.amount_in / 2 // 高风险时建议减半金额
            },
        })
    }

    /// 获取今日交易量
    pub fn get_daily_volume(&self) -> Result<u64> {
        let current_time = Clock::get()?.unix_timestamp;
        let day_in_seconds = 24 * 60 * 60;

        if current_time - self.daily_volume_reset_at >= day_in_seconds {
            Ok(0) // 新的一天，返回0
        } else {
            Ok(self.daily_volume)
        }
    }
}

/// 风险等级枚举
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
    Critical,
}

/// 路由风险评估结果
#[derive(Debug, Clone)]
pub struct RouteRiskEvaluation {
    pub risk_score: u8,
    pub warnings: Vec<String>,
    pub is_approved: bool,
    pub recommended_max_amount: u64,
}

/// 暂停原因枚举
pub mod suspension_reasons {
    pub const RISK_TOO_HIGH: u8 = 1;
    pub const SUSPICIOUS_ACTIVITY: u8 = 2;
    pub const RATE_LIMIT_EXCEEDED: u8 = 3;
    pub const ADMIN_SUSPENSION: u8 = 4;
    pub const SECURITY_VIOLATION: u8 = 5;
}