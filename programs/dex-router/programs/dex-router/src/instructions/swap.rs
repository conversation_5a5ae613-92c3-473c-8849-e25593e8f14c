use anchor_lang::prelude::*;
use anchor_spl::{
    associated_token::AssociatedToken,
    token::{Token, Mint},
    token_interface::{TokenInterface, TokenAccount as TokenAccountInterface}
};
use crate::{
    constants::{RouteConfig, seeds::*},
    state::{RouterConfig},
    error::RouteError,
    routing::RouteExecutor,
    utils::validation::*,
};

/// SwapAccounts 指令参数
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct SwapArgs {
    /// 路由配置
    pub route_config: RouteConfig,
    pub amount_in: u64,
    pub expect_amount_out: u64,
    pub min_return: u64,
}

/// SwapAccounts 指令账SwapArgs户结构
#[derive(Accounts)]
#[instruction(args: SwapArgs)]
pub struct SwapAccounts<'info> {
    /// 用户账户（必须签名）
    #[account(mut)]
    pub user: Signer<'info>,

    /// 路由器全局配置
    #[account(
        seeds = [CONFIG],
        bump,
        constraint = !config.emergency_stop @ RouteError::GlobalEmergencyStop
    )]
    pub config: Account<'info, RouterConfig>,

    /// 源代币账户（用户持有）
    #[account(
        mut,
        constraint = source_token_account.owner == user.key(),
        constraint = source_token_account.mint == args.route_config.routes.first().unwrap().input_mint,
        constraint = source_token_account.amount >= args.route_config.amount_in @ RouteError::InsufficientBalance
    )]
    pub source_token_account: Box<InterfaceAccount<'info, TokenAccountInterface>>,

    /// 目标代币账户（用户持有）
    #[account(
        mut,
        constraint = destination_token_account.owner == user.key(),
        constraint = destination_token_account.mint == args.route_config.routes.last().unwrap().output_mint
    )]
    pub destination_token_account: Box<InterfaceAccount<'info, TokenAccountInterface>>,

    /// 源代币mint
    #[account(
        constraint = source_mint.key() == args.route_config.routes.first().unwrap().input_mint
    )]
    pub source_mint: Account<'info, Mint>,

    /// 目标代币mint
    #[account(
        constraint = destination_mint.key() == args.route_config.routes.last().unwrap().output_mint
    )]
    pub destination_mint: Account<'info, Mint>,

    /// Token程序
    pub token_program: Program<'info, Token>,

    /// Token接口程序
    pub token_interface_program: Interface<'info, TokenInterface>,

    /// 关联代币程序
    pub associated_token_program: Program<'info, AssociatedToken>,

    /// 系统程序
    pub system_program: Program<'info, System>,
}

impl<'info> SwapAccounts<'info> {
    /// 验证路由配置的约束条件
    pub fn validate_route_constraints(&self, args: &SwapArgs) -> Result<()> {
        let config = &args.route_config;

        // 验证路由步骤数量
        require!(
            config.routes.len() >= 1 && config.routes.len() <= 6,
            RouteError::InvalidRouteSteps
        );

        // 验证金额
        require!(
            config.amount_in > 0,
            RouteError::ZeroAmount
        );

        require!(
            config.min_amount_out > 0,
            RouteError::ZeroAmount
        );

        // 验证滑点设置
        require!(
            config.max_slippage_bps <= 1000, // 最大10%
            RouteError::InvalidSlippage
        );

        // 验证路由连续性
        for i in 1..config.routes.len() {
            require!(
                config.routes[i-1].output_mint == config.routes[i].input_mint,
                RouteError::RouteDiscontinuity
            );
        }

        // 验证用户有足够余额
        require!(
            self.source_token_account.amount >= config.amount_in,
            RouteError::InsufficientBalance
        );

        // 验证路由在全局配置中被支持
        for route in &config.routes {
            let dex_supported = self.config.supported_dexes.contains(&route.dex);
            require!(
                dex_supported,
                RouteError::UnsupportedDex
            );
        }

        Ok(())
    }

    /// 验证账户访问权限
    pub fn validate_account_permissions(&self) -> Result<()> {
        // 验证用户签名
        require!(
            self.user.is_signer,
            RouteError::UnauthorizedUser
        );

        // 验证代币账户所有权
        require!(
            self.source_token_account.owner == self.user.key(),
            RouteError::InvalidTokenAccountOwner
        );

        require!(
            self.destination_token_account.owner == self.user.key(),
            RouteError::InvalidTokenAccountOwner
        );

        Ok(())
    }
}

/// SwapAccounts 指令处理器
pub fn swap_handler<'info>(
    ctx: Context<'_, '_, 'info, 'info, SwapAccounts<'info>>,
    args: SwapArgs,
    order_id: u64,
) -> Result<()> {
    msg!("order_id: {}", order_id);

    // 1. 验证路由配置和权限
    ctx.accounts.validate_route_constraints(&args)?;
    ctx.accounts.validate_account_permissions()?;

    // 2. 验证全局安全检查
    validate_global_security_checks(
        &ctx.accounts.config,
        &args.route_config,
    )?;

    // 3. 记录执行前的余额
    let balance_before = ctx.accounts.source_token_account.amount;
    msg!("执行前余额: {}", balance_before);

    // 4. 执行路由
    let amount_out = RouteExecutor::execute_route(
        &args.route_config,
        &[
            ctx.accounts.user.to_account_info(),
            ctx.accounts.source_token_account.to_account_info(),
            ctx.accounts.destination_token_account.to_account_info(),
            ctx.accounts.token_program.to_account_info(),
        ],
        ctx.remaining_accounts,
    )?;

    // 5. 验证输出金额满足最小要求
    require!(
        amount_out >= args.route_config.min_amount_out,
        RouteError::InsufficientOutputAmount
    );

    // 7. 记录成功事件
    msg!("路由执行成功，输出金额: {}, 订单ID: {}", amount_out, order_id);

    Ok(())
}
