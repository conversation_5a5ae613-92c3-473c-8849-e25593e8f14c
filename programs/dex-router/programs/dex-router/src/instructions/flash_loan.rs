use anchor_lang::prelude::*;
use anchor_spl::{
    associated_token::AssociatedToken,
    token::{Token, Mint},
    token_interface::{TokenInterface, TokenAccount as TokenAccountInterface}
};
use crate::{
    constants::{RouteConfig, FlashLoanConfig, seeds::*},
    state::{RouterConfig, UserPosition},
    error::RouteError,
    flash_loan::{FlashLoanManager},
    arbitrage::{ArbitrageStrategy, ArbitrageProfitCalculator},
    utils::validation::*,
};

/// FlashLoanArbitrage 指令参数
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct FlashLoanArbitrageArgs {
    /// 套利路由配置（必须是循环路由）
    pub arbitrage_config: RouteConfig,
    /// 闪电贷配置
    pub flash_loan_config: FlashLoanConfig,
    /// 订单ID
    pub order_id: u64,
    /// 最小预期利润（用于风险控制）
    pub min_profit: u64,
}

/// FlashLoanArbitrage 指令账户结构
#[derive(Accounts)]
#[instruction(args: FlashLoanArbitrageArgs)]
pub struct FlashLoanArbitrage<'info> {
    /// 用户账户（必须签名）
    #[account(mut)]
    pub user: Signer<'info>,

    /// 路由器全局配置
    #[account(
        seeds = [CONFIG],
        bump,
        constraint = !config.emergency_stop @ RouteError::GlobalEmergencyStop,
        constraint = config.flash_loan_enabled @ RouteError::FlashLoanDisabled
    )]
    pub config: Account<'info, RouterConfig>,

    /// 用户位置账户
    #[account(
        mut,
        seeds = [POSITION, user.key().as_ref()],
        bump,
        constraint = !user_position.is_suspended @ RouteError::UserSuspended,
        constraint = user_position.flash_loan_approved @ RouteError::FlashLoanNotApproved
    )]
    pub user_position: Account<'info, UserPosition>,

    /// 闪电贷临时账户（用于存储借入的代币）
    #[account(
        mut,
        seeds = [FLASH_LOAN, user.key().as_ref(), args.order_id.to_le_bytes().as_ref()],
        bump,
    )]
    /// CHECK: 临时账户由程序管理
    pub flash_loan_temp_account: UncheckedAccount<'info>,

    /// 套利起始代币账户（用户持有）
    #[account(
        mut,
        constraint = arbitrage_token_account.owner == user.key(),
        constraint = arbitrage_token_account.mint == args.arbitrage_config.routes.first().unwrap().input_mint
    )]
    pub arbitrage_token_account: Box<InterfaceAccount<'info, TokenAccountInterface>>,

    /// 套利起始代币mint
    #[account(
        constraint = arbitrage_mint.key() == args.arbitrage_config.routes.first().unwrap().input_mint
    )]
    pub arbitrage_mint: Account<'info, Mint>,

    /// 闪电贷提供者程序
    /// CHECK: 在执行时验证是否为白名单中的闪电贷提供者
    pub flash_loan_provider: UncheckedAccount<'info>,

    /// Token程序
    pub token_program: Program<'info, Token>,

    /// Token接口程序
    pub token_interface_program: Interface<'info, TokenInterface>,

    /// 关联代币程序
    pub associated_token_program: Program<'info, AssociatedToken>,

    /// 系统程序
    pub system_program: Program<'info, System>,

    /// 租金系统变量
    pub rent: Sysvar<'info, Rent>,
}

impl<'info> FlashLoanArbitrage<'info> {
    /// 验证套利配置约束
    pub fn validate_arbitrage_constraints(&self, args: &FlashLoanArbitrageArgs) -> Result<()> {
        let config = &args.arbitrage_config;

        // 验证是循环路由（套利必须是循环的）
        require!(
            config.mode == crate::constants::RoutingMode::Circular,
            RouteError::InvalidArbitrageMode
        );

        // 验证起始和结束代币相同（循环套利要求）
        let start_token = config.routes.first().unwrap().input_mint;
        let end_token = config.routes.last().unwrap().output_mint;
        require!(
            start_token == end_token,
            RouteError::NotCircularRoute
        );

        // 验证闪电贷金额不超过限制
        require!(
            args.flash_loan_config.amount <= self.config.max_flash_loan_amount,
            RouteError::FlashLoanAmountExceededNew
        );

        // 验证闪电贷提供者在白名单中
        let provider_approved = self.config.approved_flash_loan_providers
            .contains(&args.flash_loan_config.provider_program);
        require!(
            provider_approved,
            RouteError::UnauthorizedFlashLoanProvider
        );

        // 验证最小利润要求
        require!(
            args.min_profit >= self.config.min_arbitrage_profit,
            RouteError::ProfitTooLow
        );

        Ok(())
    }

    /// 验证用户权限和风险评级
    pub fn validate_user_eligibility(&self, args: &FlashLoanArbitrageArgs) -> Result<()> {
        // 验证用户签名
        require!(
            self.user.is_signer,
            RouteError::UnauthorizedUser
        );

        // 验证用户闪电贷权限
        require!(
            self.user_position.flash_loan_approved,
            RouteError::FlashLoanNotApproved
        );

        // 验证用户风险等级
        require!(
            self.user_position.risk_score <= 7, // 最大允许风险等级
            RouteError::RiskTooHigh
        );

        // 验证用户日交易限额
        let today_volume = self.user_position.get_daily_volume()?;
        let proposed_volume = args.flash_loan_config.amount;
        require!(
            today_volume + proposed_volume <= self.config.max_daily_flash_loan_per_user,
            RouteError::DailyLimitExceeded
        );

        Ok(())
    }

    /// 计算预期套利利润
    pub fn estimate_arbitrage_profit(&self, args: &FlashLoanArbitrageArgs) -> Result<u64> {
        let calculator = ArbitrageProfitCalculator::new();

        let estimated_profit = calculator.calculate_profit(
            &args.arbitrage_config,
            args.flash_loan_config.amount,
        )?;

        // 扣除闪电贷费用
        let flash_loan_fee = self.calculate_flash_loan_fee(&args.flash_loan_config)?;

        if estimated_profit <= flash_loan_fee {
            return Err(RouteError::UnprofitableArbitrage.into());
        }

        let net_profit = estimated_profit - flash_loan_fee;

        // 验证净利润满足最小要求
        require!(
            net_profit >= args.min_profit,
            RouteError::ProfitTooLow
        );

        Ok(net_profit)
    }

    /// 计算闪电贷费用
    pub fn calculate_flash_loan_fee(&self, flash_config: &FlashLoanConfig) -> Result<u64> {
        let fee_rate_bps = flash_config.max_fee_bps;
        let fee = flash_config.amount
            .checked_mul(fee_rate_bps as u64)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(10000)
            .ok_or(RouteError::MathOverflow)?;

        Ok(fee)
    }

    /// 更新用户套利统计
    pub fn update_arbitrage_statistics(&mut self, profit: u64, was_successful: bool) -> Result<()> {
        let user_position = &mut self.user_position;

        // 更新闪电贷使用次数
        user_position.flash_loan_count = user_position.flash_loan_count
            .checked_add(1)
            .ok_or(RouteError::MathOverflow)?;

        // 更新套利次数
        user_position.arbitrage_count = user_position.arbitrage_count
            .checked_add(1)
            .ok_or(RouteError::MathOverflow)?;

        if was_successful {
            // 更新成功次数和利润
            user_position.successful_arbitrages = user_position.successful_arbitrages
                .checked_add(1)
                .ok_or(RouteError::MathOverflow)?;

            user_position.total_arbitrage_profit = user_position.total_arbitrage_profit
                .checked_add(profit)
                .ok_or(RouteError::MathOverflow)?;

            // 降低风险评分（成功操作降低风险）
            if user_position.risk_score > 1 {
                user_position.risk_score = user_position.risk_score.saturating_sub(1);
            }
        } else {
            // 失败操作增加风险评分
            user_position.risk_score = user_position.risk_score.saturating_add(1).min(10);
        }

        user_position.last_arbitrage_timestamp = Clock::get()?.unix_timestamp;

        Ok(())
    }
}

/// FlashLoanArbitrage 指令处理器
pub fn flash_loan_arbitrage_handler<'info>(
    ctx: Context<'_, '_, 'info, 'info, FlashLoanArbitrage<'info>>,
    args: FlashLoanArbitrageArgs,
) -> Result<()> {
    msg!("开始闪电贷套利，订单ID: {}", args.order_id);

    // 1. 验证套利配置和用户权限
    ctx.accounts.validate_arbitrage_constraints(&args)?;
    ctx.accounts.validate_user_eligibility(&args)?;

    // 2. 预估套利利润
    let estimated_profit = ctx.accounts.estimate_arbitrage_profit(&args)?;
    msg!("预估净利润: {}", estimated_profit);

    // 3. 验证全局安全检查
    validate_arbitrage_security_checks(
        &ctx.accounts.config,
        &ctx.accounts.user_position,
        &args.arbitrage_config,
        &args.flash_loan_config,
    )?;

    // 4. 初始化闪电贷管理器
    let flash_loan_manager = FlashLoanManager::new();

    // 5. 执行闪电贷套利
    let result = flash_loan_manager.execute_flash_loan_arbitrage(
        &args.flash_loan_config,
        &args.arbitrage_config,
        ctx.remaining_accounts,
        ctx.remaining_accounts,
    );

    // 6. 处理执行结果
    let actual_profit = match result {
        Ok(profit) => {
            msg!("闪电贷套利成功，实际利润: {}", profit);

            // 验证实际利润满足最小要求
            require!(
                profit >= args.min_profit,
                RouteError::ProfitTooLow
            );

            // 更新用户统计（成功）
            ctx.accounts.update_arbitrage_statistics(profit, true)?;

            profit
        },
        Err(e) => {
            msg!("闪电贷套利失败: {:?}", e);

            // 更新用户统计（失败）
            ctx.accounts.update_arbitrage_statistics(0, false)?;

            return Err(e);
        }
    };

    // 7. 记录成功事件
    msg!(
        "闪电贷套利完成，订单ID: {}, 利润: {}",
        args.order_id,
        actual_profit
    );

    Ok(())
}
