use anchor_lang::prelude::*;

/// 日志记录工具
pub fn log_route_execution(route_id: u64, success: bool, amount: u64) {
    if success {
        msg!("路由执行成功: ID={}, 金额={}", route_id, amount);
    } else {
        msg!("路由执行失败: ID={}, 金额={}", route_id, amount);
    }
}

pub fn log_dex_operation(dex_name: &str, operation: &str, success: bool) {
    msg!("DEX操作: {} {} - 结果: {}", dex_name, operation, success);
}

pub fn log_security_event(event_type: &str, user: &Pubkey, details: &str) {
    msg!("安全事件: {} - 用户: {} - 详情: {}", event_type, user, details);
}


pub fn log_swap_balance_before(
    before_source_balance: u64,
    before_destination_balance: u64,
    amount_in: u64,
    expect_amount_out: u64,
    min_return: u64,
) {
    msg!(
        "before_source_balance: {}, before_destination_balance: {}, amount_in: {}, expect_amount_out: {}, min_return: {}",
        before_source_balance,
        before_destination_balance,
        amount_in,
        expect_amount_out,
        min_return
    );
}
