//! Raydium DEX 适配器
//!
//! 支持Raydium的CLMM和CPMM协议

use anchor_lang::prelude::*;
use anchor_lang::solana_program::{instruction::Instruction, instruction::AccountMeta};
use std::str::FromStr;
use crate::adapters::common::*;
use crate::constants::Dex;
use crate::error::RouteError;

/// Raydium CLMM适配器
pub struct RaydiumClmmProcessor;

/// Raydium CLMM 交换指令参数
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct RaydiumClmmSwapArgs {
    pub amount_in: u64,
    pub minimum_amount_out: u64,
    pub sqrt_price_limit_x64: u128,
    pub is_base_input: bool,
}

impl DexProcessor for RaydiumClmmProcessor {
    fn execute_swap_cpi<'info>(
        &self,
        accounts: &[AccountInfo<'info>],
        amount_in: u64,
        min_amount_out: u64,
        additional_args: &[u8],
    ) -> Result<u64> {
        // 验证账户数量
        require!(
            accounts.len() >= 12,
            RouteError::InvalidDexAccounts
        );

        // Raydium CLMM 账户布局（简化版）
        let pool_state = &accounts[0];
        let protocol_position = &accounts[1];
        let tick_array_lower = &accounts[2];
        let tick_array_upper = &accounts[3];
        let personal_position = &accounts[4];
        let token_account_0 = &accounts[5];
        let token_account_1 = &accounts[6];
        let token_program = &accounts[7];

        // 验证程序ID
        let raydium_clmm_program_id = Pubkey::from_str("27haf8L6oxUeXrHrgEgsexjSY5hbVUWEmvv9Nyxg8vQv")
            .map_err(|_| RouteError::SystemConfigError)?;

        // 构建交换指令数据
        let mut instruction_data = Vec::new();

        // Raydium CLMM swap 指令标识符 (方法哈希)
        let discriminator: [u8; 8] = [0xf8, 0xc6, 0x9e, 0x91, 0xe1, 0x7a, 0x99, 0xc8];
        instruction_data.extend_from_slice(&discriminator);

        // 添加交换参数
        instruction_data.extend_from_slice(&amount_in.to_le_bytes());
        instruction_data.extend_from_slice(&min_amount_out.to_le_bytes());

        // 如果有额外参数，添加到指令数据中
        if !additional_args.is_empty() {
            instruction_data.extend_from_slice(additional_args);
        }

        // 构建账户信息
        let account_infos = vec![
            pool_state.clone(),
            protocol_position.clone(),
            tick_array_lower.clone(),
            tick_array_upper.clone(),
            personal_position.clone(),
            token_account_0.clone(),
            token_account_1.clone(),
            token_program.clone(),
        ];

        // 构建指令
        let instruction = Instruction {
            program_id: raydium_clmm_program_id,
            accounts: account_infos.iter().enumerate().map(|(i, account)| {
                AccountMeta {
                    pubkey: *account.key,
                    is_signer: account.is_signer,
                    is_writable: i >= 5 && i <= 6, // token accounts 需要可写
                }
            }).collect(),
            data: instruction_data,
        };

        // 执行CPI调用
        let result = anchor_lang::solana_program::program::invoke(&instruction, &account_infos);

        match result {
            Ok(_) => {
                msg!("Raydium CLMM交换成功: {} -> {}", amount_in, min_amount_out);
                Ok(min_amount_out) // 返回预期最小输出
            },
            Err(e) => {
                msg!("Raydium CLMM交换失败: {:?}", e);
                Err(RouteError::DexCpiCallFailed.into())
            }
        }
    }

    fn get_dex_type(&self) -> Dex {
        Dex::RaydiumClmm
    }

    fn get_swap_fee_bps(&self) -> u16 {
        // Raydium CLMM 的费用通常为 0.25%
        25
    }

    fn validate_accounts(&self, accounts: &[AccountInfo]) -> Result<()> {
        require!(
            accounts.len() >= 8,
            RouteError::InvalidDexAccounts
        );

        // 验证关键账户
        let pool_state = &accounts[0];
        let token_account_0 = &accounts[5];
        let token_account_1 = &accounts[6];

        // 验证账户所有权和可写性
        require!(
            token_account_0.is_writable && token_account_1.is_writable,
            RouteError::InvalidDexAccounts
        );

        Ok(())
    }
}

/// Raydium CPMM适配器
pub struct RaydiumCpmmProcessor;

impl DexProcessor for RaydiumCpmmProcessor {
    fn execute_swap_cpi<'info>(
        &self,
        accounts: &[AccountInfo<'info>],
        amount_in: u64,
        min_amount_out: u64,
        additional_args: &[u8],
    ) -> Result<u64> {
        // 验证账户数量
        require!(
            accounts.len() >= 10,
            RouteError::InvalidDexAccounts
        );

        // Raydium CPMM 账户布局
        let amm = &accounts[0];
        let authority = &accounts[1];
        let open_orders = &accounts[2];
        let target_orders = &accounts[3];
        let coin_vault = &accounts[4];
        let pc_vault = &accounts[5];
        let market = &accounts[6];
        let user_coin_account = &accounts[7];
        let user_pc_account = &accounts[8];
        let token_program = &accounts[9];

        // 验证程序ID
        let raydium_cpmm_program_id = Pubkey::from_str("675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8")
            .map_err(|_| RouteError::SystemConfigError)?;

        // 构建交换指令数据
        let mut instruction_data = Vec::new();

        // Raydium CPMM swap 指令标识符
        let discriminator: [u8; 8] = [0x09, 0x0e, 0x6e, 0x9b, 0x8b, 0x15, 0x4a, 0xc8];
        instruction_data.extend_from_slice(&discriminator);

        // 添加交换参数
        instruction_data.extend_from_slice(&amount_in.to_le_bytes());
        instruction_data.extend_from_slice(&min_amount_out.to_le_bytes());

        // 构建账户信息
        let account_infos = vec![
            amm.clone(),
            authority.clone(),
            open_orders.clone(),
            target_orders.clone(),
            coin_vault.clone(),
            pc_vault.clone(),
            market.clone(),
            user_coin_account.clone(),
            user_pc_account.clone(),
            token_program.clone(),
        ];

        // 构建指令
        let instruction = Instruction {
            program_id: raydium_cpmm_program_id,
            accounts: account_infos.iter().enumerate().map(|(i, account)| {
                AccountMeta {
                    pubkey: *account.key,
                    is_signer: account.is_signer,
                    is_writable: match i {
                        0 | 2 | 3 | 4 | 5 | 7 | 8 => true, // 需要修改的账户
                        _ => false,
                    },
                }
            }).collect(),
            data: instruction_data,
        };

        // 执行CPI调用
        let result = anchor_lang::solana_program::program::invoke(&instruction, &account_infos);

        match result {
            Ok(_) => {
                msg!("Raydium CPMM交换成功: {} -> {}", amount_in, min_amount_out);
                Ok(min_amount_out)
            },
            Err(e) => {
                msg!("Raydium CPMM交换失败: {:?}", e);
                Err(RouteError::DexCpiCallFailed.into())
            }
        }
    }

    fn get_dex_type(&self) -> Dex {
        Dex::RaydiumCpmm
    }

    fn get_swap_fee_bps(&self) -> u16 {
        // Raydium CPMM 的费用通常为 0.25%
        25
    }

    fn validate_accounts(&self, accounts: &[AccountInfo]) -> Result<()> {
        require!(
            accounts.len() >= 10,
            RouteError::InvalidDexAccounts
        );

        // 验证关键账户的可写性
        let amm = &accounts[0];
        let user_coin_account = &accounts[7];
        let user_pc_account = &accounts[8];

        require!(
            amm.is_writable && user_coin_account.is_writable && user_pc_account.is_writable,
            RouteError::InvalidDexAccounts
        );

        Ok(())
    }
}

/// 根据DEX类型创建相应的处理器
pub fn create_raydium_processor(dex_type: Dex) -> Box<dyn DexProcessor> {
    match dex_type {
        Dex::RaydiumClmm => Box::new(RaydiumClmmProcessor),
        Dex::RaydiumCpmm => Box::new(RaydiumCpmmProcessor),
        _ => panic!("Invalid Raydium DEX type"),
    }
}

/// Raydium 交换数据结构
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct RaydiumSwapData {
    pub amount_in: u64,
    pub minimum_amount_out: u64,
    pub sqrt_price_limit_x64: u128,
    pub is_base_input: bool,
}

/// Raydium CLMM 池状态（简化版）
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct RaydiumClmmPoolState {
    pub token_mint_0: Pubkey,
    pub token_mint_1: Pubkey,
    pub token_vault_0: Pubkey,
    pub token_vault_1: Pubkey,
    pub tick_current: i32,
    pub sqrt_price_x64: u128,
    pub liquidity: u128,
}

/// Raydium CPMM AMM信息（简化版）
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct RaydiumCpmmAmmInfo {
    pub coin_mint: Pubkey,
    pub pc_mint: Pubkey,
    pub coin_vault: Pubkey,
    pub pc_vault: Pubkey,
    pub coin_vault_balance: u64,
    pub pc_vault_balance: u64,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_raydium_clmm_processor() {
        let processor = RaydiumClmmProcessor;
        assert_eq!(processor.get_dex_type(), Dex::RaydiumClmm);
        assert_eq!(processor.get_swap_fee_bps(), 25);
    }

    #[test]
    fn test_raydium_cpmm_processor() {
        let processor = RaydiumCpmmProcessor;
        assert_eq!(processor.get_dex_type(), Dex::RaydiumCpmm);
        assert_eq!(processor.get_swap_fee_bps(), 25);
    }
}
