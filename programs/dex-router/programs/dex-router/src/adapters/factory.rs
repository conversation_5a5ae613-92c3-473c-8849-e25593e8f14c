//! DEX适配器工厂
//!
//! 统一创建和管理DEX适配器的工厂模式

use crate::constants::Dex;
use crate::adapters::common::DexProcessor;
use crate::adapters::{
    raydium::{RaydiumClmmProcessor, RaydiumCpmmProcessor},
    meteora::{MeteoraLbProcessor, MeteoraAmmProcessor},
    orca::OrcaProcessor,
    pumpswap::PumpSwapProcessor,
};
use crate::error::RouteError;

/// DEX适配器工厂
/// 根据DEX类型创建相应的处理器
pub struct DexAdapterFactory;

impl DexAdapterFactory {
    /// 创建DEX处理器
    pub fn create_processor(dex_type: Dex) -> Result<Box<dyn DexProcessor>, RouteError> {
        match dex_type {
            Dex::RaydiumClmm => Ok(Box::new(RaydiumClmmProcessor)),
            Dex::RaydiumCpmm => Ok(Box::new(RaydiumCpmmProcessor)),
            Dex::MeteoraDlmm => Ok(Box::new(MeteoraLbProcessor)),
            Dex::MeteoraAmm => Ok(Box::new(MeteoraAmmProcessor)),
            Dex::Orca => Ok(Box::new(OrcaProcessor)),
            Dex::PumpSwap => Ok(Box::new(PumpSwapProcessor)),
        }
    }

    /// 获取所有支持的DEX类型
    pub fn get_supported_dexes() -> Vec<Dex> {
        vec![
            Dex::RaydiumClmm,
            Dex::RaydiumCpmm,
            Dex::MeteoraDlmm,
            Dex::MeteoraAmm,
            Dex::Orca,
            Dex::PumpSwap,
        ]
    }

    /// 检查DEX是否受支持
    pub fn is_dex_supported(dex_type: &Dex) -> bool {
        Self::get_supported_dexes().contains(dex_type)
    }

    /// 获取DEX的交换费用
    pub fn get_dex_fee_bps(dex_type: Dex) -> Result<u16, RouteError> {
        let processor = Self::create_processor(dex_type)?;
        Ok(processor.get_swap_fee_bps())
    }

    /// 批量创建多个DEX处理器
    pub fn create_multiple_processors(dex_types: &[Dex]) -> Result<Vec<Box<dyn DexProcessor>>, RouteError> {
        let mut processors = Vec::new();
        for dex_type in dex_types {
            processors.push(Self::create_processor(*dex_type)?);
        }
        Ok(processors)
    }
}

/// DEX适配器统计信息
#[derive(Debug, Clone)]
pub struct DexAdapterStats {
    pub dex_type: Dex,
    pub fee_bps: u16,
    pub supported: bool,
    pub name: String,
}

impl DexAdapterStats {
    /// 获取所有DEX的统计信息
    pub fn get_all_stats() -> Vec<Self> {
        let mut stats = Vec::new();
        
        for dex_type in DexAdapterFactory::get_supported_dexes() {
            let fee_bps = DexAdapterFactory::get_dex_fee_bps(dex_type).unwrap_or(0);
            let name = Self::get_dex_name(&dex_type);
            
            stats.push(DexAdapterStats {
                dex_type,
                fee_bps,
                supported: true,
                name,
            });
        }
        
        stats
    }

    /// 获取DEX名称
    fn get_dex_name(dex_type: &Dex) -> String {
        match dex_type {
            Dex::RaydiumClmm => "Raydium CLMM".to_string(),
            Dex::RaydiumCpmm => "Raydium CPMM".to_string(),
            Dex::MeteoraDlmm => "Meteora DLMM".to_string(),
            Dex::MeteoraAmm => "Meteora AMM".to_string(),
            Dex::Orca => "Orca Whirlpool".to_string(),
            Dex::PumpSwap => "PumpSwap".to_string(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_create_all_processors() {
        let dex_types = DexAdapterFactory::get_supported_dexes();
        
        for dex_type in dex_types {
            let processor = DexAdapterFactory::create_processor(dex_type);
            assert!(processor.is_ok(), "Failed to create processor for {:?}", dex_type);
            
            let processor = processor.unwrap();
            assert_eq!(processor.get_dex_type(), dex_type);
            assert!(processor.get_swap_fee_bps() > 0);
        }
    }

    #[test]
    fn test_dex_support_check() {
        assert!(DexAdapterFactory::is_dex_supported(&Dex::RaydiumClmm));
        assert!(DexAdapterFactory::is_dex_supported(&Dex::Orca));
        assert!(DexAdapterFactory::is_dex_supported(&Dex::PumpSwap));
    }

    #[test]
    fn test_get_dex_fees() {
        let raydium_fee = DexAdapterFactory::get_dex_fee_bps(Dex::RaydiumClmm);
        assert!(raydium_fee.is_ok());
        assert_eq!(raydium_fee.unwrap(), 25); // Raydium CLMM 0.25%

        let orca_fee = DexAdapterFactory::get_dex_fee_bps(Dex::Orca);
        assert!(orca_fee.is_ok());
        assert_eq!(orca_fee.unwrap(), 30); // Orca 0.3%

        let pump_fee = DexAdapterFactory::get_dex_fee_bps(Dex::PumpSwap);
        assert!(pump_fee.is_ok());
        assert_eq!(pump_fee.unwrap(), 100); // PumpSwap 1%
    }

    #[test]
    fn test_multiple_processors() {
        let dex_types = vec![Dex::RaydiumClmm, Dex::Orca, Dex::PumpSwap];
        let processors = DexAdapterFactory::create_multiple_processors(&dex_types);
        
        assert!(processors.is_ok());
        let processors = processors.unwrap();
        assert_eq!(processors.len(), 3);
    }

    #[test]
    fn test_adapter_stats() {
        let stats = DexAdapterStats::get_all_stats();
        assert_eq!(stats.len(), 6); // 6 supported DEXes
        
        for stat in stats {
            assert!(stat.supported);
            assert!(!stat.name.is_empty());
            assert!(stat.fee_bps > 0);
        }
    }
}