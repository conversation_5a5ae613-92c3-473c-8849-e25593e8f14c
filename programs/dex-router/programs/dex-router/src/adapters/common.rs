//! 通用DEX适配器接口和工具
//!
//! 定义标准化的DEX交互接口，统一CPI调用和验证逻辑

use anchor_lang::prelude::*;
use anchor_spl::token::TokenAccount;
use anchor_lang::solana_program::{instruction::Instruction};
use crate::error::RouteError;
use crate::constants::{Dex, Route};

/// DEX适配器核心接口
/// 所有DEX适配器都必须实现此接口
pub trait DexProcessor {
    /// 交换前预处理
    /// 返回交换前的余额，用于计算实际输出
    fn before_swap(&self, accounts: &[AccountInfo]) -> Result<u64> {
        // 默认实现：获取目标代币账户余额
        if accounts.is_empty() {
            return Ok(0);
        }

        // 假设最后一个账户是目标代币账户
        let destination_account = &accounts[accounts.len() - 1];
        let token_account = TokenAccount::try_deserialize(&mut destination_account.data.borrow().as_ref())
            .map_err(|_| RouteError::InvalidAccount)?;

        Ok(token_account.amount)
    }

    /// 执行实际的CPI调用
    /// 核心交换逻辑，每个DEX需要单独实现
    fn execute_swap_cpi<'info>(
        &self,
        accounts: &[AccountInfo<'info>],
        amount_in: u64,
        min_amount_out: u64,
        additional_args: &[u8],
    ) -> Result<u64>;

    /// 交换后处理
    /// 计算实际输出金额并进行验证
    fn after_swap(
        &self,
        accounts: &[AccountInfo],
        hop: usize,
        owner_seeds: Option<&[&[&[u8]]]>,
        before_balance: u64,
    ) -> Result<u64> {
        if accounts.is_empty() {
            return Err(RouteError::InvalidAccount.into());
        }

        // 获取目标代币账户
        let destination_account = &accounts[accounts.len() - 1];
        let token_account = TokenAccount::try_deserialize(&mut destination_account.data.borrow().as_ref())
            .map_err(|_| RouteError::InvalidAccount)?;

        let after_balance = token_account.amount;
        let actual_amount_out = after_balance.checked_sub(before_balance)
            .ok_or(RouteError::MathUnderflow)?;

        // 验证最小输出量
        require!(
            actual_amount_out > 0,
            RouteError::InsufficientLiquidity
        );

        Ok(actual_amount_out)
    }

    /// 验证账户结构
    /// 检查账户列表是否满足DEX要求
    fn validate_accounts(&self, accounts: &[AccountInfo]) -> Result<()> {
        require!(
            !accounts.is_empty(),
            RouteError::InvalidDexAccounts
        );
        Ok(())
    }

    /// 获取DEX类型
    fn get_dex_type(&self) -> Dex;

    /// 计算预期输出金额（可选实现）
    fn calculate_amount_out(&self, amount_in: u64, _additional_data: &[u8]) -> Result<u64> {
        // 默认实现：返回输入金额（1:1比率）
        Ok(amount_in)
    }

    /// 获取交换费用（基点）
    fn get_swap_fee_bps(&self) -> u16 {
        // 默认费用：0.3%
        30
    }
}

/// 标准化CPI调用封装
/// 统一处理所有DEX的交换流程
pub fn invoke_dex_swap<'info>(
    dex_processor: &dyn DexProcessor,
    accounts: &[AccountInfo<'info>],
    amount_in: u64,
    min_amount_out: u64,
    hop: usize,
    owner_seeds: Option<&[&[&[u8]]]>,
    additional_args: &[u8],
) -> Result<u64> {
    // 验证账户
    dex_processor.validate_accounts(accounts)?;

    // 交换前处理
    let before_balance = dex_processor.before_swap(accounts)?;

    // 执行实际的CPI调用
    let result = dex_processor.execute_swap_cpi(
        accounts,
        amount_in,
        min_amount_out,
        additional_args,
    );

    // 处理执行结果
    match result {
        Ok(_) => {
            // 交换后处理
            let actual_amount_out = dex_processor.after_swap(
                accounts,
                hop,
                owner_seeds,
                before_balance,
            )?;

            msg!(
                "DEX交换成功: {} -> {} (DEX: {:?})",
                amount_in,
                actual_amount_out,
                dex_processor.get_dex_type()
            );

            Ok(actual_amount_out)
        },
        Err(e) => {
            msg!(
                "DEX交换失败: {} (DEX: {:?}, 错误: {})",
                amount_in,
                dex_processor.get_dex_type(),
                e
            );
            Err(RouteError::DexCpiCallFailed.into())
        }
    }
}

/// 路由前验证逻辑
/// 统一的安全检查，适用于所有DEX
pub fn route_before_check(
    swap_authority: &AccountInfo,
    source_token_account: &Account<TokenAccount>,
    destination_token: Pubkey,
    hop: usize,
    owner_seeds: Option<&[&[&[u8]]]>,
) -> Result<()> {
    // 验证交换权限
    require!(
        swap_authority.key() == source_token_account.owner,
        RouteError::InvalidSwapAuthority
    );

    // 第一步需要签名（除非使用PDA）
    if hop == 0 && owner_seeds.is_none() {
        require!(
            swap_authority.is_signer,
            RouteError::SwapAuthorityIsNotSigner
        );
    }

    // 验证源代币余额
    require!(
        source_token_account.amount > 0,
        RouteError::InsufficientBalance
    );

    // 验证目标代币mint（如果不是第一步）
    if hop > 0 {
        // 这里可以添加更多的代币验证逻辑
        // 例如验证代币链的连续性
    }

    Ok(())
}

/// 滑点保护计算
/// 根据当前市场条件动态调整滑点保护
pub fn calculate_slippage_protection(
    expected_amount_out: u64,
    max_slippage_bps: u16,
) -> Result<u64> {
    let slippage_factor = 10000u64.checked_sub(max_slippage_bps as u64)
        .ok_or(RouteError::SlippageCalculationError)?;

    let min_amount_out = expected_amount_out
        .checked_mul(slippage_factor)
        .and_then(|x| x.checked_div(10000))
        .ok_or(RouteError::SlippageCalculationError)?;

    Ok(min_amount_out)
}

/// 账户验证工具
/// 统一的账户验证逻辑
pub struct AccountValidator;

impl AccountValidator {
    /// 验证代币账户所有者
    pub fn validate_token_account_owner(
        token_account: &Account<TokenAccount>,
        expected_owner: &Pubkey,
    ) -> Result<()> {
        require!(
            token_account.owner == *expected_owner,
            RouteError::TokenAccountOwnerMismatch
        );
        Ok(())
    }

    /// 验证代币mint匹配
    pub fn validate_token_mint(
        token_account: &Account<TokenAccount>,
        expected_mint: &Pubkey,
    ) -> Result<()> {
        require!(
            token_account.mint == *expected_mint,
            RouteError::TokenMintMismatch
        );
        Ok(())
    }

    /// 验证PDA账户
    pub fn validate_pda_account(
        account: &AccountInfo,
        seeds: &[&[u8]],
        program_id: &Pubkey,
    ) -> Result<()> {
        let (expected_pda, _) = Pubkey::find_program_address(seeds, program_id);
        require!(
            *account.key == expected_pda,
            RouteError::InvalidPda
        );
        Ok(())
    }

    /// 验证账户是否可写
    pub fn validate_account_writable(account: &AccountInfo) -> Result<()> {
        require!(
            account.is_writable,
            RouteError::InvalidAccount
        );
        Ok(())
    }
}

/// 交换指令构建器接口
/// 用于构建DEX特定的交换指令
pub trait SwapInstructionBuilder {
    /// 构建交换指令
    fn build_swap_instruction(
        &self,
        route: &Route,
        amount_in: u64,
        min_amount_out: u64,
        accounts: &[AccountInfo],
    ) -> Result<Instruction>;
}

/// 价格计算接口
/// 用于预估交换价格和影响
pub trait PriceCalculator {
    /// 计算输出金额
    fn calculate_amount_out(
        &self,
        amount_in: u64,
        reserve_in: u64,
        reserve_out: u64,
        fee_bps: u16,
    ) -> Result<u64>;

    /// 计算价格影响
    fn calculate_price_impact(
        &self,
        amount_in: u64,
        reserve_in: u64,
        reserve_out: u64,
    ) -> Result<u16>; // 返回基点
}

/// 默认价格计算器实现（AMM公式）
pub struct DefaultPriceCalculator;

impl PriceCalculator for DefaultPriceCalculator {
    fn calculate_amount_out(
        &self,
        amount_in: u64,
        reserve_in: u64,
        reserve_out: u64,
        fee_bps: u16,
    ) -> Result<u64> {
        if reserve_in == 0 || reserve_out == 0 {
            return Err(RouteError::InsufficientLiquidity.into());
        }

        // 扣除费用后的输入金额
        let amount_in_with_fee = amount_in
            .checked_mul(10000u64.checked_sub(fee_bps as u64).unwrap_or(9970))
            .and_then(|x| x.checked_div(10000))
            .ok_or(RouteError::MathOverflow)?;

        // AMM公式: amount_out = (amount_in_with_fee * reserve_out) / (reserve_in + amount_in_with_fee)
        let numerator = amount_in_with_fee
            .checked_mul(reserve_out)
            .ok_or(RouteError::MathOverflow)?;

        let denominator = reserve_in
            .checked_add(amount_in_with_fee)
            .ok_or(RouteError::MathOverflow)?;

        let amount_out = numerator
            .checked_div(denominator)
            .ok_or(RouteError::DivisionByZero)?;

        Ok(amount_out)
    }

    fn calculate_price_impact(
        &self,
        amount_in: u64,
        reserve_in: u64,
        reserve_out: u64,
    ) -> Result<u16> {
        if reserve_in == 0 || reserve_out == 0 {
            return Ok(10000); // 100% 价格影响
        }

        // 价格影响 = amount_in / (reserve_in + amount_in) * 10000
        let denominator = reserve_in
            .checked_add(amount_in)
            .ok_or(RouteError::MathOverflow)?;

        let price_impact = amount_in
            .checked_mul(10000)
            .and_then(|x| x.checked_div(denominator))
            .ok_or(RouteError::MathOverflow)?;

        Ok(price_impact as u16)
    }
}
