//! 线性路由引擎
//!
//! 实现线性路由模式：A -> B -> C -> D
//! 支持多步骤跳转的直线交换

use anchor_lang::prelude::*;
use crate::adapters::common::DexProcessor;
use crate::adapters::factory::DexAdapterFactory;
use crate::constants::{Route, RouteConfig, RoutingMode, Dex};
use crate::error::RouteError;
use crate::state::event::{RouteExecuted, SwapCompleted, route_phases};

/// 线性路由执行器
pub struct LinearRouteExecutor;

impl LinearRouteExecutor {
    /// 执行线性路由
    pub fn execute_linear_route<'info>(
        config: &RouteConfig,
        accounts: &[AccountInfo<'info>],
        remaining_accounts: &[AccountInfo<'info>],
    ) -> Result<u64> {
        // 验证路由配置
        require!(
            config.mode == RoutingMode::Linear,
            RouteError::InvalidRouteConfig
        );

        require!(
            !config.routes.is_empty() && config.routes.len() <= 6,
            RouteError::InvalidRouteSteps
        );

        // 发出路由开始事件
        emit!(RouteExecuted {
            phase: route_phases::STARTED,
            mode: config.mode.clone(),
            user: None,
            amount_in: config.amount_in,
            amount_out: 0,
            min_amount_out: config.min_amount_out,
            routes_count: config.routes.len() as u8,
            routes_executed: 0,
            total_fees: 0,
            execution_time: 0,
            success: false,
            actual_slippage_bps: 0,
            timestamp: Clock::get()?.unix_timestamp,
        });

        let mut current_amount = config.amount_in;
        let mut total_fees = 0u64;
        let mut executed_routes = Vec::new();

        // 逐步执行每个路由
        for (step, route) in config.routes.iter().enumerate() {
            msg!("执行线性路由步骤 {}/{}: {:?} -> {:?}", 
                step + 1, 
                config.routes.len(), 
                route.input_mint, 
                route.output_mint
            );

            // 获取此步骤的账户
            let step_accounts = Self::extract_step_accounts(
                remaining_accounts, 
                step, 
                route.dex
            )?;

            // 执行单步交换
            let (amount_out, step_fee) = Self::execute_single_step(
                route,
                &step_accounts,
                current_amount,
                step == config.routes.len() - 1, // 是否为最后一步
                config.min_amount_out,
            )?;

            // 更新状态
            current_amount = amount_out;
            total_fees = total_fees.checked_add(step_fee)
                .ok_or(RouteError::MathOverflow)?;

            // 记录执行的路由
            executed_routes.push(route.clone());

            // 发出单步完成事件
            emit!(SwapCompleted {
                dex: route.dex,
                input_mint: route.input_mint,
                output_mint: route.output_mint,
                amount_in: if step == 0 { config.amount_in } else { 0 }, // 只在第一步记录初始输入
                amount_out,
                fee_paid: step_fee,
                step: step as u8,
            });
        }

        // 验证最终输出是否满足最小要求
        require!(
            current_amount >= config.min_amount_out,
            RouteError::InsufficientOutput
        );

        // 发出路由完成事件
        let actual_slippage_bps = if config.amount_in > 0 {
            let expected = (config.amount_in as f64 * 0.997) as u64;
            if expected > current_amount {
                (((expected - current_amount) * 10000) / expected) as u16
            } else { 0 }
        } else { 0 };

        emit!(RouteExecuted {
            phase: route_phases::COMPLETED,
            mode: config.mode.clone(),
            user: None,
            amount_in: config.amount_in,
            amount_out: current_amount,
            min_amount_out: config.min_amount_out,
            routes_count: config.routes.len() as u8,
            routes_executed: executed_routes.len() as u8,
            total_fees,
            execution_time: 0, // 将在后续版本中实现时间追踪
            success: true,
            actual_slippage_bps,
            timestamp: Clock::get()?.unix_timestamp,
        });

        msg!("线性路由执行成功: {} -> {}, 总费用: {}", 
             config.amount_in, current_amount, total_fees);

        Ok(current_amount)
    }

    /// 执行单个路由步骤
    fn execute_single_step<'info>(
        route: &Route,
        accounts: &[AccountInfo<'info>],
        amount_in: u64,
        is_last_step: bool,
        min_final_amount: u64,
    ) -> Result<(u64, u64)> {
        // 创建DEX处理器
        let processor = DexAdapterFactory::create_processor(route.dex)?;

        // 验证账户
        processor.validate_accounts(accounts)?;

        // 计算最小输出金额（如果是最后一步，使用用户指定的最小输出）
        let min_amount_out = if is_last_step {
            min_final_amount
        } else {
            // 对于中间步骤，允许一定的滑点
            amount_in.checked_mul(95)
                .and_then(|x| x.checked_div(100))
                .ok_or(RouteError::MathOverflow)?
        };

        // 计算费用
        let fee_bps = processor.get_swap_fee_bps();
        let fee_amount = amount_in.checked_mul(fee_bps as u64)
            .and_then(|x| x.checked_div(10000))
            .ok_or(RouteError::MathOverflow)?;

        // 执行交换CPI调用
        let amount_out = processor.execute_swap_cpi(
            accounts,
            amount_in,
            min_amount_out,
            &route.swap_data,
        )?;

        Ok((amount_out, fee_amount))
    }

    /// 从remaining_accounts中提取特定步骤的账户
    fn extract_step_accounts<'info>(
        remaining_accounts: &[AccountInfo<'info>],
        step: usize,
        dex: Dex,
    ) -> Result<Vec<AccountInfo<'info>>> {
        // 根据DEX类型确定需要的账户数量
        let accounts_per_step = match dex {
            Dex::RaydiumClmm | Dex::RaydiumCpmm => 8,
            Dex::MeteoraDlmm => 10,
            Dex::MeteoraAmm => 8,
            Dex::Orca => 9,
            Dex::PumpSwap => 8,
        };

        // 计算起始索引
        let start_index = step * accounts_per_step;
        let end_index = start_index + accounts_per_step;

        // 验证账户数量
        require!(
            end_index <= remaining_accounts.len(),
            RouteError::InvalidDexAccounts
        );

        // 提取账户
        let step_accounts: Vec<AccountInfo<'info>> = remaining_accounts[start_index..end_index]
            .iter()
            .cloned()
            .collect();

        Ok(step_accounts)
    }

    /// 预估线性路由的输出金额
    pub fn estimate_linear_output(
        config: &RouteConfig,
        additional_data: &[Vec<u8>],
    ) -> Result<u64> {
        require!(
            config.mode == RoutingMode::Linear,
            RouteError::InvalidRouteConfig
        );

        require!(
            additional_data.len() >= config.routes.len(),
            RouteError::InsufficientData
        );

        let mut current_amount = config.amount_in;

        for (i, route) in config.routes.iter().enumerate() {
            let processor = DexAdapterFactory::create_processor(route.dex)?;
            
            // 使用处理器计算输出金额
            current_amount = processor.calculate_amount_out(
                current_amount,
                &additional_data[i],
            )?;

            // 应用滑点保护
            current_amount = current_amount.checked_mul(95)
                .and_then(|x| x.checked_div(100))
                .ok_or(RouteError::MathOverflow)?;
        }

        Ok(current_amount)
    }

    /// 验证线性路由配置
    pub fn validate_linear_config(config: &RouteConfig) -> Result<()> {
        require!(
            config.mode == RoutingMode::Linear,
            RouteError::InvalidRouteConfig
        );

        require!(
            !config.routes.is_empty(),
            RouteError::EmptyRoute
        );

        require!(
            config.routes.len() <= 6,
            RouteError::TooManyRouteSteps
        );

        require!(
            config.amount_in > 0,
            RouteError::ZeroAmount
        );

        require!(
            config.min_amount_out > 0,
            RouteError::ZeroAmount
        );

        // 验证路由连续性 - 前一步的输出应该是下一步的输入
        for i in 1..config.routes.len() {
            require!(
                config.routes[i - 1].output_mint == config.routes[i].input_mint,
                RouteError::InvalidRouteConfig
            );
        }

        // 验证每个路由的DEX是否受支持
        for route in &config.routes {
            require!(
                DexAdapterFactory::is_dex_supported(&route.dex),
                RouteError::UnsupportedDex
            );
        }

        Ok(())
    }

    /// 计算线性路由的总费用
    pub fn calculate_total_fees(config: &RouteConfig) -> Result<u64> {
        let mut total_fee_bps = 0u16;

        for route in &config.routes {
            let fee_bps = DexAdapterFactory::get_dex_fee_bps(route.dex)?;
            total_fee_bps = total_fee_bps.checked_add(fee_bps)
                .ok_or(RouteError::MathOverflow)?;
        }

        let total_fee = config.amount_in.checked_mul(total_fee_bps as u64)
            .and_then(|x| x.checked_div(10000))
            .ok_or(RouteError::MathOverflow)?;

        Ok(total_fee)
    }
}

/// 线性路由统计信息
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct LinearRouteStats {
    pub steps: u8,
    pub total_fee_bps: u16,
    pub estimated_output: u64,
    pub price_impact_bps: u16,
    pub execution_time_estimate: u32, // 秒
}

impl LinearRouteStats {
    /// 计算线性路由统计
    pub fn calculate(config: &RouteConfig) -> Result<Self> {
        LinearRouteExecutor::validate_linear_config(config)?;

        let steps = config.routes.len() as u8;
        
        // 计算总费用
        let mut total_fee_bps = 0u16;
        for route in &config.routes {
            let fee_bps = DexAdapterFactory::get_dex_fee_bps(route.dex)?;
            total_fee_bps = total_fee_bps.checked_add(fee_bps)
                .ok_or(RouteError::MathOverflow)?;
        }

        // 估算执行时间（每步约2秒）
        let execution_time_estimate = (steps as u32) * 2;

        // 简化的价格影响估算
        let price_impact_bps = (steps as u16) * 50; // 每步约0.5%价格影响

        Ok(LinearRouteStats {
            steps,
            total_fee_bps,
            estimated_output: 0, // 需要额外数据计算
            price_impact_bps,
            execution_time_estimate,
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::constants::*;

    fn create_test_linear_config() -> RouteConfig {
        RouteConfig {
            mode: RoutingMode::Linear,
            routes: vec![
                Route {
                    dex: Dex::RaydiumClmm,
                    input_mint: Pubkey::default(),
                    output_mint: Pubkey::new_unique(),
                    swap_data: vec![],
                },
                Route {
                    dex: Dex::Orca,
                    input_mint: Pubkey::new_unique(),
                    output_mint: Pubkey::new_unique(),
                    swap_data: vec![],
                },
            ],
            amount_in: 1_000_000, // 1 USDC
            min_amount_out: 900_000, // 0.9 USDC
            max_slippage_bps: 300, // 3%
            flash_loan: None,
        }
    }

    #[test]
    fn test_validate_linear_config() {
        let mut config = create_test_linear_config();
        
        // 修复路由连续性
        config.routes[1].input_mint = config.routes[0].output_mint;
        
        let result = LinearRouteExecutor::validate_linear_config(&config);
        assert!(result.is_ok());
    }

    #[test]
    fn test_invalid_route_continuity() {
        let config = create_test_linear_config();
        // 故意不修复连续性，应该失败
        
        let result = LinearRouteExecutor::validate_linear_config(&config);
        assert!(result.is_err());
    }

    #[test]
    fn test_calculate_total_fees() {
        let mut config = create_test_linear_config();
        config.routes[1].input_mint = config.routes[0].output_mint;
        
        let total_fees = LinearRouteExecutor::calculate_total_fees(&config).unwrap();
        assert!(total_fees > 0);
        
        // Raydium CLMM (25 bps) + Orca (30 bps) = 55 bps
        // 1,000,000 * 55 / 10000 = 5,500
        assert_eq!(total_fees, 5_500);
    }

    #[test]
    fn test_linear_route_stats() {
        let mut config = create_test_linear_config();
        config.routes[1].input_mint = config.routes[0].output_mint;
        
        let stats = LinearRouteStats::calculate(&config).unwrap();
        assert_eq!(stats.steps, 2);
        assert_eq!(stats.total_fee_bps, 55); // 25 + 30
        assert_eq!(stats.execution_time_estimate, 4); // 2 steps * 2 seconds
        assert_eq!(stats.price_impact_bps, 100); // 2 steps * 50 bps
    }

    #[test]
    fn test_extract_step_accounts_calculation() {
        // 测试账户索引计算逻辑
        let raydium_accounts_needed = 8;
        let orca_accounts_needed = 9;
        
        // 第0步 Raydium: 索引 0-7
        assert_eq!(0 * raydium_accounts_needed, 0);
        assert_eq!(0 * raydium_accounts_needed + raydium_accounts_needed, 8);
        
        // 第1步 Orca: 索引 8-16
        assert_eq!(1 * raydium_accounts_needed, 8);
        assert_eq!(1 * raydium_accounts_needed + orca_accounts_needed, 17);
    }

    #[test]
    fn test_too_many_steps() {
        let mut config = create_test_linear_config();
        
        // 添加过多步骤
        for i in 0..5 {
            config.routes.push(Route {
                dex: Dex::RaydiumClmm,
                input_mint: Pubkey::new_unique(),
                output_mint: Pubkey::new_unique(),
                swap_data: vec![],
            });
        }
        
        let result = LinearRouteExecutor::validate_linear_config(&config);
        assert!(result.is_err());
    }
}